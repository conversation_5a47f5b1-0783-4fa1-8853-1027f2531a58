import React from 'react';
import styles from '../index.sass';

interface ControlPanelProps {
  showHeatNetwork: boolean;
  showGasNetwork: boolean;
  showFacilities: boolean;
  onToggleLayer: (layerType: 'heat' | 'gas' | 'facilities') => void;
}

/**
 * 地图控制面板组件
 * 提供图层切换按钮（热网、气网、设施）
 */
const ControlPanel: React.FC<ControlPanelProps> = ({
  showHeatNetwork,
  showGasNetwork,
  showFacilities,
  onToggleLayer,
}) => {
  return (
    <div className={styles.controlPanel}>
      <button
        type="button"
        className={`${styles.controlButton} ${
          showHeatNetwork ? styles.active : ''
        }`}
        onClick={() => onToggleLayer('heat')}
        title="热网系统"
      >
        🔥
      </button>
      <button
        type="button"
        className={`${styles.controlButton} ${
          showGasNetwork ? styles.active : ''
        }`}
        onClick={() => onToggleLayer('gas')}
        title="气网系统"
      >
        ⛽
      </button>
      <button
        type="button"
        className={`${styles.controlButton} ${
          showFacilities ? styles.active : ''
        }`}
        onClick={() => onToggleLayer('facilities')}
        title="设施"
      >
        🏭
      </button>
    </div>
  );
};

export default ControlPanel;
