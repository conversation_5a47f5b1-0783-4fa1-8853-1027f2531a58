@import '@/utils/helpers.sass'
.mapContainer
  position: relative
  width: 100%
  flex: 1
  min-height: 0
  border-radius: px(8)
  overflow: hidden
  background: #1a1a1a

.map
  width: 100%
  height: 100%

.loading
  position: absolute
  top: 0
  left: 0
  right: 0
  bottom: 0
  display: flex
  align-items: center
  justify-content: center
  background: rgba(0, 0, 0, 0.8)
  z-index: 1000

.loadingText
  color: #fff
  font-size: px(16)
  font-weight: 500

// 控制面板样式
.controlPanel
  position: absolute
  top: px(20)
  right: px(20)
  display: flex
  flex-direction: column
  gap: px(10)
  z-index: 1000

.controlButton
  width: px(50)
  height: px(50)
  border: none
  border-radius: px(8)
  background: rgba(0, 0, 0, 0.8)
  color: #fff
  font-size: px(20)
  cursor: pointer
  transition: all 0.3s ease
  display: flex
  align-items: center
  justify-content: center

  &:hover
    background: rgba(0, 0, 0, 0.9)
    transform: scale(1.05)

  &.active
    background: rgba(24, 144, 255, 0.8)
    box-shadow: 0 0 px(10) rgba(24, 144, 255, 0.5)

// 图例样式
.legend
  position: absolute
  bottom: px(20)
  left: px(20)
  background: rgba(0, 0, 0, 0.8)
  border-radius: px(8)
  padding: px(15)
  color: #fff
  z-index: 1000
  min-width: px(150)

.legendTitle
  font-size: px(14)
  font-weight: 600
  margin-bottom: px(10)
  color: #fff

.legendItem
  display: flex
  align-items: center
  gap: px(8)
  margin-bottom: px(6)
  font-size: px(12)

  &:last-child
    margin-bottom: 0

.legendIcon
  width: px(20)
  height: px(3)
  border-radius: px(2)

  &.heat
    background: #ff7875

  &.gas
    background: #52c41a

.legendDot
  width: px(12)
  height: px(12)
  border-radius: 50%
  border: px(2) solid #fff

  &.running
    background: #52c41a

  &.maintenance
    background: #faad14

  &.error
    background: #ff4d4f

  &.stopped
    background: #8c8c8c

// 信息面板样式
.infoPanel
  position: absolute
  top: px(20)
  left: px(20)
  background: rgba(0, 0, 0, 0.9)
  border-radius: px(8)
  padding: px(20)
  color: #fff
  z-index: 1000
  min-width: px(250)
  max-width: px(350)

.infoPanelTitle
  font-size: px(16)
  font-weight: 600
  margin-bottom: px(15)
  color: #fff
  border-bottom: px(1) solid rgba(255, 255, 255, 0.2)
  padding-bottom: px(8)

.infoItem
  display: flex
  justify-content: space-between
  align-items: center
  margin-bottom: px(8)
  font-size: px(14)

  &:last-child
    margin-bottom: 0

.infoLabel
  color: #bfbfbf
  margin-right: px(10)

.infoValue
  color: #fff
  font-weight: 500

  &.running
    color: #52c41a

  &.maintenance
    color: #faad14

  &.error
    color: #ff4d4f

  &.stopped
    color: #8c8c8c

  &.normal
    color: #52c41a

  &.warning
    color: #faad14

// 响应式设计
@media (max-width: 768px)
  .controlPanel
    top: px(10)
    right: px(10)

  .controlButton
    width: px(40)
    height: px(40)
    font-size: px(16)

  .legend
    bottom: px(10)
    left: px(10)
    padding: px(10)
    min-width: px(120)

  .legendTitle
    font-size: px(12)

  .legendItem
    font-size: px(11)

  .infoPanel
    top: px(10)
    left: px(10)
    padding: px(15)
    min-width: px(200)
    max-width: px(280)

  .infoPanelTitle
    font-size: px(14)

  .infoItem
    font-size: px(12)

// 动画效果
@keyframes pulse
  0%
    opacity: 1
  50%
    opacity: 0.5
  100%
    opacity: 1

.pulsing
  animation: pulse 2s infinite

@keyframes flow
  0%
    stroke-dashoffset: 0
  100%
    stroke-dashoffset: 20

.flowing
  stroke-dasharray: 5 5
  animation: flow 1s linear infinite

// 地图控件样式覆盖
:global
  .mapboxgl-ctrl-bottom-left
    display: none

  .mapboxgl-ctrl-bottom-right
    display: none

  .mapboxgl-popup-content
    background: rgba(255, 255, 255, 0.95)
    border-radius: px(8)
    box-shadow: 0 px(4) px(12) rgba(0, 0, 0, 0.3)

  .mapboxgl-popup-tip
    border-top-color: rgba(255, 255, 255, 0.95)

  .mapboxgl-popup-close-button
    color: #666
    font-size: px(18)
    padding: px(5)

    &:hover
      color: #333

// 故障悬浮弹窗样式
.faultPopup
  position: fixed
  z-index: 1000
  pointer-events: none

.faultPopupContent
  background: rgba(0, 0, 0, 0.9)
  color: #fff
  padding: px(12)
  border-radius: px(6)
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3)
  min-width: px(200)
  max-width: px(300)
  font-size: px(12)
  line-height: 1.4
  border: 1px solid rgba(255, 255, 255, 0.2)

.faultTitle
  font-weight: 600
  font-size: px(14)
  margin-bottom: px(8)
  padding: px(4) px(8)
  border-radius: px(4)
  text-align: center

  &.level1
    background: linear-gradient(135deg, #ff4d4f, #cf1322)
    color: #fff

  &.level2
    background: linear-gradient(135deg, #faad14, #d48806)
    color: #fff

  &.level3
    background: linear-gradient(135deg, #52c41a, #389e0d)
    color: #fff

.faultTime
  color: #bfbfbf
  font-size: px(11)
  margin-bottom: px(6)

.faultDescription
  color: #fff
  margin-bottom: px(6)
  font-weight: 500

.faultLocation
  color: #91d5ff
  font-size: px(11)
  margin-bottom: px(4)

.faultStatus
  color: #ffd666
  font-size: px(11)
  font-weight: 500
