import mapboxgl from 'mapbox-gl';
import { useEffect, useRef } from 'react';
import { FacilityData, PipelineData, RealTimeData } from './types';

interface MapLayerManagerProps {
  map: mapboxgl.Map | null;
  data: RealTimeData;
  showHeatNetwork: boolean;
  showGasNetwork: boolean;
  showFacilities: boolean;
  onPipelineClick: (pipeline: PipelineData, e: mapboxgl.MapMouseEvent) => void;
  onFacilityClick: (facility: FacilityData) => void;
  onPipelineHover: (
    pipeline: PipelineData | null,
    e?: mapboxgl.MapMouseEvent,
  ) => void;
  onFacilityHover: (
    facility: FacilityData | null,
    e?: mapboxgl.MapMouseEvent,
  ) => void;
}

/**
 * 地图图层管理组件
 * 负责地图图层的创建、更新和交互事件处理
 */
const MapLayerManager: React.FC<MapLayerManagerProps> = ({
  map,
  data,
  showHeatNetwork,
  showGasNetwork,
  showFacilities,
  onPipelineClick,
  onFacilityClick,
  onPipelineHover,
  onFacilityHover,
}) => {
  const isInitialized = useRef(false);

  // 添加1km×1km网格图层
  const addGridLayer = () => {
    if (!map) return;

    const gridSize = 0.01; // 约1km
    const centerLng = 116.3984;
    const centerLat = 39.9098;
    const gridExtent = 0.05; // 网格范围

    const gridFeatures = [];
    for (
      let lng = centerLng - gridExtent;
      lng <= centerLng + gridExtent;
      lng += gridSize
    ) {
      for (
        let lat = centerLat - gridExtent;
        lat <= centerLat + gridExtent;
        lat += gridSize
      ) {
        gridFeatures.push({
          type: 'Feature' as const,
          properties: {},
          geometry: {
            type: 'Polygon' as const,
            coordinates: [
              [
                [lng, lat],
                [lng + gridSize, lat],
                [lng + gridSize, lat + gridSize],
                [lng, lat + gridSize],
                [lng, lat],
              ],
            ],
          },
        });
      }
    }

    map.addSource('grid', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: gridFeatures,
      },
    });

    map.addLayer({
      id: 'grid',
      type: 'line',
      source: 'grid',
      layout: {},
      paint: {
        'line-color': '#333333',
        'line-width': 0.5,
        'line-opacity': 0.3,
      },
    });
  };

  // 添加流向箭头
  const addFlowArrows = (
    sourceId: string,
    pipelines: PipelineData[],
    color: string,
  ) => {
    if (!map) return;

    const arrowFeatures = pipelines.map((pipeline) => {
      const coords = pipeline.coordinates;
      const midIndex = Math.floor(coords.length / 2);
      const midPoint = coords[midIndex];

      // 计算箭头方向
      const prevPoint = coords[midIndex - 1] || coords[0];
      const nextPoint = coords[midIndex + 1] || coords[coords.length - 1];

      const angle =
        (Math.atan2(nextPoint[1] - prevPoint[1], nextPoint[0] - prevPoint[0]) *
          180) /
        Math.PI;

      return {
        type: 'Feature' as const,
        properties: {
          id: pipeline.id,
          direction: pipeline.direction,
          angle: pipeline.direction === 'backward' ? angle + 180 : angle,
        },
        geometry: {
          type: 'Point' as const,
          coordinates: midPoint,
        },
      };
    });

    map.addSource(`${sourceId}-arrows`, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: arrowFeatures,
      },
    });

    map.addLayer({
      id: `${sourceId}-arrows`,
      type: 'symbol',
      source: `${sourceId}-arrows`,
      layout: {
        'text-field': '▶',
        'text-font': ['Open Sans Regular'],
        'text-size': 16,
        'text-rotate': ['get', 'angle'],
        'text-rotation-alignment': 'map',
        'text-allow-overlap': true,
      },
      paint: {
        'text-color': color,
        'text-halo-color': '#000000',
        'text-halo-width': 1,
      },
    });
  };

  // 添加流量标签
  const addFlowLabels = (
    sourceId: string,
    pipelines: PipelineData[],
    unit: string,
  ) => {
    if (!map) return;

    const labelFeatures = pipelines.map((pipeline) => {
      const coords = pipeline.coordinates;
      const midIndex = Math.floor(coords.length / 2);
      const midPoint = coords[midIndex];

      return {
        type: 'Feature' as const,
        properties: {
          id: pipeline.id,
          label: `${pipeline.flow.toFixed(1)} ${unit}`,
        },
        geometry: {
          type: 'Point' as const,
          coordinates: midPoint,
        },
      };
    });

    map.addSource(`${sourceId}-labels`, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: labelFeatures,
      },
    });

    map.addLayer({
      id: `${sourceId}-labels`,
      type: 'symbol',
      source: `${sourceId}-labels`,
      layout: {
        'text-field': ['get', 'label'],
        'text-font': ['Open Sans Regular'],
        'text-size': 12,
        'text-offset': [0, -2],
        'text-anchor': 'bottom',
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 2,
      },
    });
  };

  // 添加管道图层
  const addPipelineLayers = () => {
    if (!map) return;

    const heatPipelines = data.pipelines.filter((p) => p.type === 'heat');
    const gasPipelines = data.pipelines.filter((p) => p.type === 'gas');

    // 添加热网管道
    if (heatPipelines.length > 0) {
      map.addSource('heat-pipelines', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: heatPipelines.map((pipeline) => ({
            type: 'Feature',
            properties: {
              id: pipeline.id,
              flow: pipeline.flow,
              direction: pipeline.direction,
              status: pipeline.status,
            },
            geometry: {
              type: 'LineString',
              coordinates: pipeline.coordinates,
            },
          })),
        },
      });

      map.addLayer({
        id: 'heat-pipelines',
        type: 'line',
        source: 'heat-pipelines',
        paint: {
          'line-color': [
            'case',
            ['==', ['get', 'status'], 'error'],
            '#ff4d4f',
            ['==', ['get', 'status'], 'warning'],
            '#faad14',
            '#ff7875',
          ],
          'line-width': 4,
          'line-opacity': 0.8,
        },
      });

      // 添加流量标签和流向箭头
      addFlowLabels('heat-pipelines', heatPipelines, 'GJ/h');
      addFlowArrows('heat-pipelines', heatPipelines, '#ff7875');
    }

    // 添加气网管道
    if (gasPipelines.length > 0) {
      map.addSource('gas-pipelines', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: gasPipelines.map((pipeline) => ({
            type: 'Feature',
            properties: {
              id: pipeline.id,
              flow: pipeline.flow,
              direction: pipeline.direction,
              status: pipeline.status,
            },
            geometry: {
              type: 'LineString',
              coordinates: pipeline.coordinates,
            },
          })),
        },
      });

      map.addLayer({
        id: 'gas-pipelines',
        type: 'line',
        source: 'gas-pipelines',
        paint: {
          'line-color': [
            'case',
            ['==', ['get', 'status'], 'error'],
            '#ff4d4f',
            ['==', ['get', 'status'], 'warning'],
            '#faad14',
            '#52c41a',
          ],
          'line-width': 4,
          'line-opacity': 0.8,
        },
      });

      // 添加流量标签和流向箭头
      addFlowLabels('gas-pipelines', gasPipelines, 'm³/h');
      addFlowArrows('gas-pipelines', gasPipelines, '#52c41a');
    }
  };

  // 添加设施图层
  const addFacilityLayers = () => {
    if (!map) return;

    map.addSource('facilities', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: data.facilities.map((facility) => ({
          type: 'Feature',
          properties: {
            id: facility.id,
            type: facility.type,
            name: facility.name,
            status: facility.status,
            capacity: facility.capacity,
            currentOutput: facility.currentOutput,
          },
          geometry: {
            type: 'Point',
            coordinates: facility.coordinates,
          },
        })),
      },
    });

    map.addLayer({
      id: 'facilities',
      type: 'circle',
      source: 'facilities',
      paint: {
        'circle-radius': [
          'case',
          ['==', ['get', 'type'], 'power_plant'],
          12,
          ['==', ['get', 'type'], 'substation'],
          8,
          6,
        ],
        'circle-color': [
          'case',
          ['==', ['get', 'status'], 'error'],
          '#ff4d4f',
          ['==', ['get', 'status'], 'maintenance'],
          '#faad14',
          ['==', ['get', 'status'], 'stopped'],
          '#8c8c8c',
          '#52c41a',
        ],
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff',
      },
    });

    // 添加设施标签
    map.addLayer({
      id: 'facility-labels',
      type: 'symbol',
      source: 'facilities',
      layout: {
        'text-field': ['get', 'name'],
        'text-font': ['Open Sans Regular'],
        'text-offset': [0, 2],
        'text-anchor': 'top',
        'text-size': 12,
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 1,
      },
    });
  };

  // 初始化图层
  const initializeLayers = () => {
    if (!map || isInitialized.current) return;

    addGridLayer();
    addPipelineLayers();
    addFacilityLayers();
    isInitialized.current = true;
  };

  // 更新地图数据
  const updateMapData = () => {
    if (!map || !isInitialized.current) return;

    // 更新热网管道数据
    const heatPipelines = data.pipelines.filter((p) => p.type === 'heat');
    if (heatPipelines.length > 0 && map.getSource('heat-pipelines')) {
      (map.getSource('heat-pipelines') as mapboxgl.GeoJSONSource).setData({
        type: 'FeatureCollection',
        features: heatPipelines.map((pipeline) => ({
          type: 'Feature' as const,
          properties: {
            id: pipeline.id,
            flow: pipeline.flow,
            direction: pipeline.direction,
            status: pipeline.status,
          },
          geometry: {
            type: 'LineString' as const,
            coordinates: pipeline.coordinates,
          },
        })),
      });

      // 更新流量标签
      if (map.getSource('heat-pipelines-labels')) {
        (
          map.getSource('heat-pipelines-labels') as mapboxgl.GeoJSONSource
        ).setData({
          type: 'FeatureCollection',
          features: heatPipelines.map((pipeline) => {
            const coords = pipeline.coordinates;
            const midIndex = Math.floor(coords.length / 2);
            const midPoint = coords[midIndex];
            return {
              type: 'Feature' as const,
              properties: {
                id: pipeline.id,
                label: `${pipeline.flow.toFixed(1)} GJ/h`,
              },
              geometry: {
                type: 'Point' as const,
                coordinates: midPoint,
              },
            };
          }),
        });
      }
    }

    // 更新气网管道数据
    const gasPipelines = data.pipelines.filter((p) => p.type === 'gas');
    if (gasPipelines.length > 0 && map.getSource('gas-pipelines')) {
      (map.getSource('gas-pipelines') as mapboxgl.GeoJSONSource).setData({
        type: 'FeatureCollection',
        features: gasPipelines.map((pipeline) => ({
          type: 'Feature' as const,
          properties: {
            id: pipeline.id,
            flow: pipeline.flow,
            direction: pipeline.direction,
            status: pipeline.status,
          },
          geometry: {
            type: 'LineString' as const,
            coordinates: pipeline.coordinates,
          },
        })),
      });

      // 更新流量标签
      if (map.getSource('gas-pipelines-labels')) {
        (
          map.getSource('gas-pipelines-labels') as mapboxgl.GeoJSONSource
        ).setData({
          type: 'FeatureCollection',
          features: gasPipelines.map((pipeline) => {
            const coords = pipeline.coordinates;
            const midIndex = Math.floor(coords.length / 2);
            const midPoint = coords[midIndex];
            return {
              type: 'Feature' as const,
              properties: {
                id: pipeline.id,
                label: `${pipeline.flow.toFixed(1)} m³/h`,
              },
              geometry: {
                type: 'Point' as const,
                coordinates: midPoint,
              },
            };
          }),
        });
      }
    }

    // 更新设施数据
    if (map.getSource('facilities')) {
      (map.getSource('facilities') as mapboxgl.GeoJSONSource).setData({
        type: 'FeatureCollection',
        features: data.facilities.map((facility) => ({
          type: 'Feature' as const,
          properties: {
            id: facility.id,
            type: facility.type,
            name: facility.name,
            status: facility.status,
            capacity: facility.capacity,
            currentOutput: facility.currentOutput,
          },
          geometry: {
            type: 'Point' as const,
            coordinates: facility.coordinates,
          },
        })),
      });
    }
  };

  // 添加地图交互事件
  const addMapInteractions = () => {
    if (!map) return;

    // 管道点击事件
    map.on('click', 'heat-pipelines', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        const pipeline = data.pipelines.find((p) => p.id === properties?.id);
        if (pipeline) {
          onPipelineClick(pipeline, e);
        }
      }
    });

    map.on('click', 'gas-pipelines', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        const pipeline = data.pipelines.find((p) => p.id === properties?.id);
        if (pipeline) {
          onPipelineClick(pipeline, e);
        }
      }
    });

    map.on('click', 'facilities', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        const facility = data.facilities.find((f) => f.id === properties?.id);
        if (facility) {
          onFacilityClick(facility);
        }
      }
    });

    // 设施悬浮事件
    map.on('mouseenter', 'facilities', (e) => {
      if (e.features && e.features[0]) {
        map.getCanvas().style.cursor = 'pointer';
        const feature = e.features[0];
        const facility = data.facilities.find(
          (f) => f.id === feature.properties?.id,
        );
        if (facility) {
          onFacilityHover(facility, e);
        }
      }
    });

    map.on('mouseleave', 'facilities', () => {
      map.getCanvas().style.cursor = '';
      onFacilityHover(null);
    });

    // 管道悬浮事件
    map.on('mouseenter', 'heat-pipelines', (e) => {
      if (e.features && e.features[0]) {
        map.getCanvas().style.cursor = 'pointer';
        const feature = e.features[0];
        const pipeline = data.pipelines.find(
          (p) => p.id === feature.properties?.id,
        );
        if (pipeline) {
          onPipelineHover(pipeline, e);
        }
      }
    });

    map.on('mouseleave', 'heat-pipelines', () => {
      map.getCanvas().style.cursor = '';
      onPipelineHover(null);
    });

    map.on('mouseenter', 'gas-pipelines', (e) => {
      if (e.features && e.features[0]) {
        map.getCanvas().style.cursor = 'pointer';
        const feature = e.features[0];
        const pipeline = data.pipelines.find(
          (p) => p.id === feature.properties?.id,
        );
        if (pipeline) {
          onPipelineHover(pipeline, e);
        }
      }
    });

    map.on('mouseleave', 'gas-pipelines', () => {
      map.getCanvas().style.cursor = '';
      onPipelineHover(null);
    });
  };

  // 切换图层显示
  const toggleLayerVisibility = (
    layerType: 'heat' | 'gas' | 'facilities',
    visible: boolean,
  ) => {
    if (!map) return;

    const visibility = visible ? 'visible' : 'none';

    switch (layerType) {
      case 'heat':
        if (map.getLayer('heat-pipelines')) {
          map.setLayoutProperty('heat-pipelines', 'visibility', visibility);
        }
        if (map.getLayer('heat-pipelines-arrows')) {
          map.setLayoutProperty(
            'heat-pipelines-arrows',
            'visibility',
            visibility,
          );
        }
        if (map.getLayer('heat-pipelines-labels')) {
          map.setLayoutProperty(
            'heat-pipelines-labels',
            'visibility',
            visibility,
          );
        }
        break;
      case 'gas':
        if (map.getLayer('gas-pipelines')) {
          map.setLayoutProperty('gas-pipelines', 'visibility', visibility);
        }
        if (map.getLayer('gas-pipelines-arrows')) {
          map.setLayoutProperty(
            'gas-pipelines-arrows',
            'visibility',
            visibility,
          );
        }
        if (map.getLayer('gas-pipelines-labels')) {
          map.setLayoutProperty(
            'gas-pipelines-labels',
            'visibility',
            visibility,
          );
        }
        break;
      case 'facilities':
        if (map.getLayer('facilities')) {
          map.setLayoutProperty('facilities', 'visibility', visibility);
        }
        if (map.getLayer('facility-labels')) {
          map.setLayoutProperty('facility-labels', 'visibility', visibility);
        }
        break;
    }
  };

  // 初始化图层
  useEffect(() => {
    if (map) {
      initializeLayers();
      addMapInteractions();
    }
  }, [map]);

  // 更新数据
  useEffect(() => {
    updateMapData();
  }, [data]);

  // 控制图层显示
  useEffect(() => {
    toggleLayerVisibility('heat', showHeatNetwork);
  }, [showHeatNetwork]);

  useEffect(() => {
    toggleLayerVisibility('gas', showGasNetwork);
  }, [showGasNetwork]);

  useEffect(() => {
    toggleLayerVisibility('facilities', showFacilities);
  }, [showFacilities]);

  return null; // 这是一个逻辑组件，不渲染任何UI
};

export default MapLayerManager;
