import React from 'react';
import styles from '../index.sass';
import { FacilityData } from './types';

interface FacilityInfoPanelProps {
  facility: FacilityData;
  onClose: () => void;
  getFacilityTypeText: (type: string) => string;
  getStatusText: (status: string) => string;
}

/**
 * 设施信息面板组件
 * 显示选中设施的详细信息
 */
const FacilityInfoPanel: React.FC<FacilityInfoPanelProps> = ({
  facility,
  onClose,
  getFacilityTypeText,
  getStatusText,
}) => {
  return (
    <div className={styles.infoPanel}>
      <div className={styles.infoPanelTitle}>{facility.name}</div>
      <div className={styles.infoItem}>
        <span className={styles.infoLabel}>类型:</span>
        <span className={styles.infoValue}>
          {getFacilityTypeText(facility.type)}
        </span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.infoLabel}>状态:</span>
        <span className={`${styles.infoValue} ${styles[facility.status]}`}>
          {getStatusText(facility.status)}
        </span>
      </div>
      {facility.capacity && (
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>容量:</span>
          <span className={styles.infoValue}>{facility.capacity} MW</span>
        </div>
      )}
      {facility.currentOutput && (
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>当前输出:</span>
          <span className={styles.infoValue}>{facility.currentOutput} MW</span>
        </div>
      )}
      <button
        type="button"
        onClick={onClose}
        style={{
          position: 'absolute',
          top: '5px',
          right: '5px',
          background: 'none',
          border: 'none',
          fontSize: '16px',
          cursor: 'pointer',
          color: '#fff',
        }}
      >
        ×
      </button>
    </div>
  );
};

export default FacilityInfoPanel;
